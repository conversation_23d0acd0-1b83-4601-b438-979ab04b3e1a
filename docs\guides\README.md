# 📚 Guides & References

This folder contains comprehensive guides and reference materials for the 1Tech Academy platform.

## 📋 Contents

### Documentation Guides
- **[Documentation Index](./documentation-index.md)** - Complete map of all documentation
- **[Features Index](./features-index.md)** - Overview of all platform features

### Development Guides
- **[Git Commit Guide](./git-commit-guide.md)** - Commit message standards and procedures

## 🎯 Purpose

This documentation provides:
- **Navigation Help**: Easy access to all documentation sections
- **Reference Materials**: Quick lookup for common procedures
- **Best Practices**: Standardized approaches for development tasks
- **Index Files**: Organized overview of documentation structure

## 🚀 Quick Start

1. **New to the Project**: Start with [Documentation Index](./documentation-index.md)
2. **Looking for Features**: Check [Features Index](./features-index.md)
3. **Contributing Code**: Follow [Git Commit Guide](./git-commit-guide.md)

## 📞 Support

For questions about documentation structure or guides, refer to the main [Documentation](../README.md) or contact the development team.
